import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import * as bcrypt from 'bcrypt';
import { PrismaService } from '../prisma/prisma.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    @InjectPinoLogger(AuthService.name)
    private readonly logger: PinoLogger,
  ) {}

  async register(registerDto: RegisterDto) {
    const startTime = Date.now();
    const operationId = `reg_${Date.now()}`;
    const { email, password, firstName, lastName, avatar, dateOfBirth, phoneNumber, address, role } = registerDto;

    this.logger.debug('User registration started', {
      operationId,
      email,
      role: role || 'CASHIER',
      hasAvatar: !!avatar,
      hasDateOfBirth: !!dateOfBirth,
      hasPhoneNumber: !!phoneNumber,
      hasAddress: !!address,
      timestamp: new Date().toISOString(),
    });

    try {
      // Check if user already exists
      this.logger.trace('Checking for existing user', {
        operationId,
        email,
      });

      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        this.logger.warn('Registration failed - user already exists', {
          operationId,
          email,
          existingUserId: existingUser.id,
          existingUserRole: existingUser.role,
        });
        throw new ConflictException('User with this email already exists');
      }

      // Validate password strength
      this.logger.trace('Validating password strength', {
        operationId,
        email,
        passwordLength: password.length,
      });

      if (password.length < 8) {
        this.logger.warn('Registration failed - weak password', {
          operationId,
          email,
          passwordLength: password.length,
          minimumRequired: 8,
        });
        throw new BadRequestException('Password must be at least 8 characters long');
      }

      // Hash password
      this.logger.trace('Hashing password', {
        operationId,
        email,
        saltRounds: 12,
      });

      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      this.logger.trace('Creating user in database', {
        operationId,
        email,
        role: role || 'CASHIER',
      });

      const user = await this.prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          avatar,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          phoneNumber,
          address,
          role: role || 'CASHIER',
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          createdAt: true,
        },
      });

      // Generate JWT token
      this.logger.trace('Generating JWT token', {
        operationId,
        userId: user.id,
        email: user.email,
      });

      const payload = { sub: user.id, email: user.email };
      const accessToken = this.jwtService.sign(payload);

      const duration = Date.now() - startTime;
      this.logger.info('User registration completed successfully', {
        operationId,
        userId: user.id,
        email: user.email,
        role: user.role,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
      });

      return {
        user,
        accessToken,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('User registration failed', error, {
        operationId,
        email,
        role: role || 'CASHIER',
        duration: `${duration}ms`,
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  async login(loginDto: LoginDto) {
    const startTime = Date.now();
    const operationId = `login_${Date.now()}`;
    const { email, password } = loginDto;

    this.logger.debug('User login attempt started', {
      operationId,
      email,
      timestamp: new Date().toISOString(),
      // Note: password is automatically redacted by Pino configuration
    });

    try {
      // Find user by email
      this.logger.trace('Looking up user by email', {
        operationId,
        email,
      });

      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        this.logger.warn('Login failed - user not found', {
          operationId,
          email,
          reason: 'user_not_found',
          timestamp: new Date().toISOString(),
        });
        throw new UnauthorizedException('Invalid credentials');
      }

      // Verify password
      this.logger.trace('Verifying password', {
        operationId,
        userId: user.id,
        email,
      });

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        this.logger.warn('Login failed - invalid password', {
          operationId,
          userId: user.id,
          email,
          reason: 'invalid_password',
          timestamp: new Date().toISOString(),
        });
        throw new UnauthorizedException('Invalid credentials');
      }

      // Update last login
      this.logger.trace('Updating last login timestamp', {
        operationId,
        userId: user.id,
        email,
      });

      await this.updateLastLogin(user.id);

      // Generate JWT token
      this.logger.trace('Generating JWT token', {
        operationId,
        userId: user.id,
        email,
        role: user.role,
      });

      const payload = { sub: user.id, email: user.email, role: user.role };
      const accessToken = this.jwtService.sign(payload);

      const duration = Date.now() - startTime;
      this.logger.info('User login completed successfully', {
        operationId,
        userId: user.id,
        email,
        role: user.role,
        duration: `${duration}ms`,
        lastLoginAt: new Date().toISOString(),
        timestamp: new Date().toISOString(),
      });

      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          dateOfBirth: user.dateOfBirth,
          phoneNumber: user.phoneNumber,
          address: user.address,
          role: user.role,
          createdAt: user.createdAt,
        },
        accessToken,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('User login failed', error, {
        operationId,
        email,
        duration: `${duration}ms`,
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  async validateUser(email: string, password: string): Promise<any> {
    const operationId = `validate_${Date.now()}`;

    this.logger.trace('User validation started', {
      operationId,
      email,
      timestamp: new Date().toISOString(),
    });

    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (user && (await bcrypt.compare(password, user.password))) {
        const { password: _, ...result } = user;

        this.logger.trace('User validation successful', {
          operationId,
          userId: user.id,
          email,
          role: user.role,
        });

        return result;
      }

      this.logger.trace('User validation failed', {
        operationId,
        email,
        reason: user ? 'invalid_password' : 'user_not_found',
      });

      return null;
    } catch (error) {
      this.logger.error('User validation error', error, {
        operationId,
        email,
        errorType: error.constructor.name,
      });
      return null;
    }
  }

  async findUserById(id: string) {
    const operationId = `find_user_${Date.now()}`;

    this.logger.trace('Finding user by ID', {
      operationId,
      userId: id,
      timestamp: new Date().toISOString(),
    });

    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (user) {
        this.logger.trace('User found successfully', {
          operationId,
          userId: id,
          email: user.email,
          role: user.role,
        });
      } else {
        this.logger.warn('User not found', {
          operationId,
          userId: id,
        });
      }

      return user;
    } catch (error) {
      this.logger.error('Error finding user by ID', error, {
        operationId,
        userId: id,
        errorType: error.constructor.name,
      });
      throw error;
    }
  }

  async updateProfile(id: string, updateProfileDto: UpdateProfileDto) {
    const operationId = `update_profile_${Date.now()}`;
    const { firstName, lastName, avatar, dateOfBirth, phoneNumber, address } = updateProfileDto;

    this.logger.debug('User profile update started', {
      operationId,
      userId: id,
      fieldsToUpdate: {
        firstName: !!firstName,
        lastName: !!lastName,
        avatar: !!avatar,
        dateOfBirth: !!dateOfBirth,
        phoneNumber: !!phoneNumber,
        address: !!address,
      },
      timestamp: new Date().toISOString(),
    });

    try {
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: {
          firstName,
          lastName,
          avatar,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
          phoneNumber,
          address,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      this.logger.info('User profile updated successfully', {
        operationId,
        userId: id,
        email: updatedUser.email,
        updatedFields: Object.keys(updateProfileDto).filter(key => updateProfileDto[key] !== undefined),
        timestamp: new Date().toISOString(),
      });

      return updatedUser;
    } catch (error) {
      this.logger.error('User profile update failed', error, {
        operationId,
        userId: id,
        updateData: {
          firstName: !!firstName,
          lastName: !!lastName,
          avatar: !!avatar,
          dateOfBirth: !!dateOfBirth,
          phoneNumber: !!phoneNumber,
          address: !!address,
        },
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  async updateLastLogin(id: string) {
    const operationId = `update_last_login_${Date.now()}`;

    this.logger.trace('Updating last login timestamp', {
      operationId,
      userId: id,
      timestamp: new Date().toISOString(),
    });

    try {
      await this.prisma.user.update({
        where: { id },
        data: { lastLoginAt: new Date() },
      });

      this.logger.debug('Last login timestamp updated successfully', {
        operationId,
        userId: id,
        lastLoginAt: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to update last login timestamp', error, {
        operationId,
        userId: id,
        errorType: error.constructor.name,
        errorMessage: error.message,
      });
      throw error;
    }
  }
}
